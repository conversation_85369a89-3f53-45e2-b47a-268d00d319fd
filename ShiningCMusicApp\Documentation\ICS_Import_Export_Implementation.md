# ICS Import/Export Implementation

## Overview
This document describes the implementation of ICS (iCalendar) import/export functionality in the Lessons.razor page using Syncfusion Scheduler's built-in capabilities.

## ✅ IMPLEMENTATION COMPLETED

### Features Implemented

**✅ ICS Export Functionality:**
- Role-based export permissions (Admin exports all events, Tutors/Students export only their own)
- Automatic filename generation with timestamps and user role identification
- Uses Syncfusion's built-in `ExportToICalendarAsync` method
- Success/error feedback through custom dialog service

**✅ ICS Import Functionality:**
- Admin-only import permissions (only administrators can import events)
- File validation (checks for .ics extension and MIME type)
- Uses Syncfusion's built-in `ImportICalendarAsync` method
- Hidden file uploader triggered programmatically
- Automatic data refresh after successful import

**✅ User Interface:**
- Export dropdown button with calendar icon
- Import button with upload icon
- Responsive design that stacks buttons on mobile
- Bootstrap-styled components with consistent theming

## Technical Implementation

### 1. UI Components Added

#### Export Dropdown Button
```razor
<div class="btn-group" role="group">
    <button type="button" class="btn btn-outline-primary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
        <i class="bi bi-download"></i>
        <span class="d-none d-sm-inline ms-1">Export</span>
    </button>
    <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="#" @onclick="OnExportToIcs">
            <i class="bi bi-calendar-event me-2"></i>Export to ICS
        </a></li>
    </ul>
</div>
```

#### Import Button
```razor
<button class="btn btn-outline-success btn-sm" @onclick="OnImportClick">
    <i class="bi bi-upload"></i>
    <span class="d-none d-sm-inline ms-1">Import</span>
</button>
```

#### Hidden File Uploader
```razor
<SfUploader @ref="uploaderRef"
           AllowedExtensions=".ics"
           CssClass="d-none"
           Multiple="false"
           ID="icsUploader">
    <UploaderEvents ValueChange="OnFileChange"></UploaderEvents>
</SfUploader>
```

### 2. Core Methods

#### Export Method
```csharp
private async Task OnExportToIcs()
{
    // Get events based on user role
    var eventsToExport = GetEventsForExport();
    
    // Generate role-based filename
    var fileName = GenerateExportFileName();
    
    // Export using Syncfusion
    await scheduleRef.ExportToICalendarAsync(fileName, eventsToExport);
    
    // Show success message
    await DialogService.ShowSuccessAsync("Export Successful", $"Calendar events have been exported to {fileName}.ics");
}
```

#### Import Method
```csharp
private async Task OnFileChange(UploadChangeEventArgs args)
{
    // Validate file type
    if (!file.FileInfo.Name.EndsWith(".ics", StringComparison.OrdinalIgnoreCase))
    {
        await DialogService.ShowWarningAsync("Invalid File Type", "Please select a valid ICS calendar file.");
        return;
    }

    // Read file content
    using var reader = new StreamReader(stream);
    var fileContent = await reader.ReadToEndAsync();

    // Import using Syncfusion
    await scheduleRef.ImportICalendarAsync(fileContent);
    await LoadData(); // Refresh data
}
```

### 3. Role-Based Permissions

#### Export Permissions
- **Administrator**: Can export all events in the system
- **Tutor**: Can export only their assigned lessons
- **Student**: Can export only their enrolled lessons

#### Import Permissions
- **Administrator**: Can import ICS files (adds events to the system)
- **Tutor/Student**: Cannot import (import button hidden/disabled)

#### Filename Generation
```csharp
private string GenerateExportFileName()
{
    var timestamp = DateTime.Now.ToString("yyyy-MM-dd");
    
    if (IsAdmin) return $"ShiningCMusic_AllLessons_{timestamp}";
    if (IsTutor) return $"ShiningCMusic_MyLessons_Tutor_{timestamp}";
    if (IsStudent) return $"ShiningCMusic_MyLessons_Student_{timestamp}";
    
    return $"ShiningCMusic_Lessons_{timestamp}";
}
```

### 4. JavaScript Integration

#### File Upload Trigger
```javascript
window.triggerFileUpload = function () {
    // Find the hidden uploader by ID and then look for the file input
    const uploaderContainer = document.getElementById('icsUploader');
    if (uploaderContainer) {
        const fileInput = uploaderContainer.querySelector('input[type="file"]');
        if (fileInput) {
            fileInput.click();
        }
    }
};
```

### 5. Package Dependencies

#### Added Syncfusion Package
```xml
<PackageReference Include="Syncfusion.Blazor.Inputs" Version="29.2.11" />
```

#### JavaScript Files
- `wwwroot/js/ics-import-export.js` - Helper functions for file handling

### 6. CSS Styling

#### Responsive Button Layout
```css
/* Stack import/export buttons on mobile */
@media (max-width: 767.98px) {
    .d-flex.gap-2.flex-wrap {
        flex-direction: column;
        align-items: stretch;
    }
}
```

## User Workflow

### Exporting Events
1. Click the "Export" dropdown button
2. Select "Export to ICS" from the dropdown
3. File automatically downloads with role-appropriate filename
4. Success message confirms export completion

### Importing Events (Admin Only)
1. Click the "Import" button
2. File picker opens automatically
3. Select a valid .ics file
4. Events are imported and calendar refreshes
5. Success message confirms import completion

## Error Handling

### Export Errors
- No events available for export
- Syncfusion export failures
- File system errors

### Import Errors
- Invalid file type (not .ics)
- Empty files
- Malformed ICS content
- Permission denied (non-admin users)
- File size limits (10MB maximum)

## Benefits

### 1. Standards Compliance
- Uses iCalendar (RFC 5545) standard format
- Compatible with Google Calendar, Outlook, Apple Calendar, etc.
- Preserves recurring event patterns and exceptions

### 2. User Experience
- Intuitive UI with clear icons and labels
- Role-appropriate functionality
- Responsive design for mobile devices
- Clear success/error feedback

### 3. Security
- Role-based access control
- File type validation
- Size limits to prevent abuse
- Admin-only import permissions

### 4. Technical Benefits
- Leverages Syncfusion's tested export/import functionality
- Minimal custom code required
- Automatic handling of complex calendar data
- Built-in error handling and validation

## Future Enhancements

### Potential Improvements
1. **Selective Export**: Allow users to select date ranges or specific events
2. **Import Preview**: Show preview of events before importing
3. **Bulk Operations**: Import/export with progress indicators
4. **Format Options**: Support for other calendar formats (CSV, Excel)
5. **Scheduling**: Automated periodic exports
6. **Cloud Integration**: Direct integration with Google Calendar, Outlook, etc.

## Conclusion

The ICS import/export functionality provides a professional, standards-compliant way for users to share and backup their lesson schedules. The implementation follows the user's requirements for role-based permissions while leveraging Syncfusion's robust calendar handling capabilities.
