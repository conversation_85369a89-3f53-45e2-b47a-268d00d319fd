/* Lessons page specific styles */

/* Schedule responsive height */
.schedule-container .e-schedule {
    height: 800px !important;
}

/* Tutor Colors collapse styling */
.card-header .btn-link {
    color: inherit !important;
    text-decoration: none !important;
}

.card-header .btn-link:hover {
    color: inherit !important;
}

.card-header .btn-link i.bi-chevron-down {
    transition: transform 0.2s ease;
}

.card-header .btn-link[aria-expanded="true"] i.bi-chevron-down {
    transform: rotate(180deg);
}

/* Custom footer styling */
.custom-editor-footer {
    display: flex;
    justify-content: flex-end;
}

/* Mobile lesson card styling */
.clickable-card {
    transition: all 0.2s ease-in-out;
    border: 1px solid #dee2e6;
}

    .clickable-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-color: #0066cc;
    }

    .clickable-card:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

.lesson-card {
    border-radius: 8px;
}

    .lesson-card .card-body {
        position: relative;
    }

/* Ensure editor dialog appears above mobile list view */
.e-schedule-dialog {
    z-index: 1049 !important;
}

    .e-schedule-dialog .e-dlg-overlay {
        z-index: 1048 !important;
    }

/* Style the desktop QuickInfo popup edit and delete buttons as outline icon-only buttons */
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit,
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete {
    width: 40px !important;
    min-width: 40px !important;
    height: 40px !important;
    padding: 8px !important;
    border-radius: 4px !important;
    font-size: 16px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.2s !important;
    border: 1px solid !important;
    cursor: pointer !important;
    background: transparent !important;
}


/* Edit button styling */
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit {
    border-color: #0066cc !important;
    color: #0066cc !important;
}

/* Hide button text on desktop - target the actual text content */
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit,
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete {
    font-size: 0 !important;
    text-indent: -9999px !important;
}

/* Ensure icons are still visible by resetting their font-size */
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit::before,
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete::before {
    font-size: 16px !important;
    text-indent: 0 !important;
}

    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit:hover {
        background: #0066cc !important;
        color: white !important;
    }

    /* Add pencil icon to edit button */
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit::before {
        content: "\F4CB" !important; /* Bootstrap pencil icon */
        font-family: "bootstrap-icons" !important;
        font-size: 16px !important;
    }


/* Delete button styling */
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete {
    border-color: #dc3545 !important;
    color: #dc3545 !important;
}

    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete:hover {
        background: #dc3545 !important;
        color: white !important;
    }

    /* Add trash icon to delete button */
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete::before {
        content: "\F5DE" !important; /* Bootstrap trash icon */
        font-family: "bootstrap-icons" !important;
        font-size: 16px !important;
    }

    /* Ensure icons inherit button colors */
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit .e-btn-icon,
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete .e-btn-icon,
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit .e-icons,
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete .e-icons {
        color: inherit !important;
    }

/* Style the button container */
.e-quick-popup-wrapper .e-event-popup .e-popup-footer {
    display: flex !important;
    gap: 8px !important;
    justify-content: flex-end !important;
    padding: 16px !important;
    border-top: 1px solid #eee !important;
}

/* Mobile-specific fixes for Schedule dialog */
@media (max-width: 991.98px) {
    /* Mobile schedule height */
    .schedule-container .e-schedule {
        height: 600px !important;
    }

    /* Fix z-index to appear above sticky header and mobile list view */
    .e-schedule-dialog.e-dialog,
    .e-schedule-dialog {
        z-index: 1050 !important;
    }

        /* Fix dialog positioning and height for mobile */
        .e-schedule-dialog .e-dlg-container {
            max-height: calc(100vh - 80px) !important;
            margin-top: 40px !important;
            margin-bottom: 40px !important;
        }

        /* Make dialog content scrollable */
        .e-schedule-dialog .e-dlg-content {
            max-height: calc(100vh - 160px) !important;
            overflow-y: auto !important;
            padding: 15px !important;
        }

    /* Ensure custom editor is scrollable */
    .custom-event-editor {
        max-height: calc(100vh - 200px) !important;
        overflow-y: auto !important;
        padding: 15px !important;
    }

    /* Force QuickInfo popup to not be full screen on mobile */
    .e-quick-popup-wrapper {
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        width: 90% !important;
        max-width: 400px !important;
        height: auto !important;
        max-height: 80vh !important;
        z-index: 1047 !important;
        bottom: auto !important;
    }

    .e-quick-popup-wrapper .e-event-popup {
        position: relative !important;
        width: 100% !important;
        height: auto !important;
        max-height: none !important;
        transform: none !important;
        top: auto !important;
        left: auto !important;
        right: auto !important;
        bottom: auto !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
    }

    /* Force the popup to fit content exactly */
    .e-quick-popup-wrapper .e-event-popup .e-popup-content {
        height: auto !important;
        min-height: auto !important;
        max-height: none !important;
        overflow: visible !important;
        padding: 16px !important;
    }

    /* Remove any fixed heights that might cause extra space */
    .e-quick-popup-wrapper .e-event-popup .e-popup-content .quick-info {
        height: auto !important;
        min-height: auto !important;
    }

    /* Hide the footer area completely on mobile for all users */
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer {
        display: none !important;
    }

    /* Compact form spacing on mobile */
    .custom-event-editor .row {
        margin-bottom: 0.75rem;
    }

    .custom-event-editor .mb-3 {
        margin-bottom: 0.75rem !important;
    }

    /* Custom footer mobile adjustments */
    .custom-editor-footer {
        padding: 10px 15px;
        gap: 8px;
    }
}

/* Extra small mobile adjustments */
@media (max-width: 575.98px) {
    .e-schedule-dialog .e-dlg-container {
        margin: 10px !important;
        max-height: calc(100vh - 20px) !important;
    }

    .e-schedule-dialog .e-dlg-content {
        max-height: calc(100vh - 100px) !important;
        padding: 10px !important;
    }

    .custom-event-editor {
        max-height: calc(100vh - 120px) !important;
        padding: 10px !important;
    }

        /* Stack form fields vertically on very small screens */
        .custom-event-editor .col-md-6 {
            margin-bottom: 0.5rem;
        }

    /* Custom footer extra small mobile adjustments */
    .custom-editor-footer {
        padding: 8px 10px;
        gap: 6px;
    }

        .custom-editor-footer .btn {
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
        }
}

/* Import/Export button styling */
.btn-group .dropdown-toggle {
    border-top-right-radius: 0.375rem !important;
    border-bottom-right-radius: 0.375rem !important;
}

.dropdown-menu .dropdown-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
}

.dropdown-menu .dropdown-item i {
    width: 16px;
    text-align: center;
}

.dropdown-menu .dropdown-item:hover {
    background-color: #f8f9fa;
}

/* Custom Import Button Styling */
.import-button-container {
    position: relative;
    display: inline-block;
}

.import-button-container input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    z-index: 2;
}

.import-button-container .btn {
    position: relative;
    z-index: 1;
    pointer-events: none;
    /* Ensure same size as export button */
    min-width: auto;
    height: auto;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.375rem;
}

/* Import button hover effect - triggered by file input hover */
.import-button-container:hover .btn {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.15s ease-in-out;
}

/* Import button active effect - triggered by file input active */
.import-button-container:active .btn {
    background-color: #0a58ca;
    border-color: #0a53be;
    color: #fff;
    transform: translateY(0);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.125);
}

/* Force both buttons to have exactly the same size */
.btn-group .btn.dropdown-toggle,
.import-button-container .btn {
    min-width: 60px !important;
    height: 38px !important;
    padding: 0.375rem 0.75rem !important;
    font-size: 0.875rem !important;
    line-height: 1.5 !important;
    border-radius: 0.375rem !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-sizing: border-box !important;
}

/* Override Bootstrap btn-sm default sizing */
.btn-group .btn-sm.dropdown-toggle,
.import-button-container .btn-sm {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.875rem !important;
    line-height: 1.5 !important;
    border-radius: 0.375rem !important;
}

/* Adjust dropdown arrow positioning to not affect button size */
.btn-group .dropdown-toggle::after {
    margin-left: 0.255em !important;
    vertical-align: 0.255em !important;
}

/* Ensure text and icons are properly centered */
.btn-group .btn.dropdown-toggle .bi,
.import-button-container .btn .bi {
    margin-right: 0.25rem;
}

/* Make sure both buttons have same text spacing */
.btn-group .btn.dropdown-toggle span,
.import-button-container .btn span {
    margin-left: 0.25rem !important;
}

/* Focus effects for accessibility */
.import-button-container input[type="file"]:focus + .btn {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    outline: none;
}

.btn-group .btn.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Lesson action buttons styling */
.lesson-action-btn {
    min-width: 150px;
}

/* Custom recurrence editor styling */
.custom-recurrence-editor {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    background-color: #f8f9fa;
}

/* Mobile responsive - Full width buttons */
@media (max-width: 768px) {
    /* Force the button container to be full width and stack vertically */
    .d-flex.gap-2.flex-wrap {
        display: flex !important;
        flex-direction: column !important;
        gap: 0.75rem !important;
        width: 100% !important;
    }

    /* Force all direct children to be full width */
    .d-flex.gap-2.flex-wrap > * {
        width: 100% !important;
        max-width: 100% !important;
        flex: none !important;
        display: block !important;
    }

    /* Force lesson action buttons to full width */
    .lesson-action-btn {
        width: 100% !important;
        min-width: 100% !important;
        max-width: 100% !important;
        display: block !important;
    }

    /* Force all buttons to be full width */
    .d-flex.gap-2.flex-wrap .btn,
    .d-flex.gap-2.flex-wrap button {
        width: 100% !important;
        min-width: 100% !important;
        max-width: 100% !important;
        display: block !important;
        justify-content: center !important;
        text-align: center !important;
        padding: 0.75rem 1rem !important;
        min-height: 44px !important;
        touch-action: manipulation;
        box-sizing: border-box !important;
    }

    /* Force button groups to full width */
    .d-flex.gap-2.flex-wrap .btn-group {
        width: 100% !important;
        max-width: 100% !important;
        display: block !important;
    }

    .d-flex.gap-2.flex-wrap .btn-group .btn {
        width: 100% !important;
        max-width: 100% !important;
        display: block !important;
        justify-content: center !important;
        border-radius: 0.375rem !important;
    }

    /* Force import button container to full width */
    .d-flex.gap-2.flex-wrap .import-button-container {
        width: 100% !important;
        max-width: 100% !important;
        display: block !important;
    }

    .d-flex.gap-2.flex-wrap .import-button-container .btn {
        width: 100% !important;
        max-width: 100% !important;
        display: block !important;
        justify-content: center !important;
    }

    /* Disable hover effects on mobile */
    .import-button-container:hover .btn,
    .btn-group .btn:hover {
        transform: none !important;
        box-shadow: none !important;
    }
}

/* Additional breakpoint for very small screens */
@media (max-width: 576px) {
    .d-flex.gap-2.flex-wrap,
    .d-flex.gap-2.flex-wrap > *,
    .d-flex.gap-2.flex-wrap .btn,
    .d-flex.gap-2.flex-wrap button,
    .lesson-action-btn {
        width: 100% !important;
        min-width: 100% !important;
        max-width: 100% !important;
        display: block !important;
    }
}

/* High specificity override for mobile - target the exact container */
@media screen and (max-width: 768px) {
    .card-header .d-flex.gap-2.flex-wrap,
    .card-body .d-flex.gap-2.flex-wrap {
        display: flex !important;
        flex-direction: column !important;
        width: 100% !important;
    }

    .card-header .d-flex.gap-2.flex-wrap > *,
    .card-body .d-flex.gap-2.flex-wrap > * {
        width: 100% !important;
        max-width: 100% !important;
        flex: 1 1 100% !important;
    }

    .card-header .lesson-action-btn,
    .card-body .lesson-action-btn {
        width: 100% !important;
        min-width: 100% !important;
        max-width: 100% !important;
    }

    .card-header .btn,
    .card-body .btn {
        width: 100% !important;
        min-width: 100% !important;
        max-width: 100% !important;
        display: block !important;
    }
}
