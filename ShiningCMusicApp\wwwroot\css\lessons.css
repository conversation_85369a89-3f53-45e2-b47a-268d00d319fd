/* Lessons page specific styles */

/* Schedule responsive height */
.schedule-container .e-schedule {
    height: 800px !important;
}

/* Tutor Colors collapse styling */
.card-header .btn-link {
    color: inherit !important;
    text-decoration: none !important;
}

.card-header .btn-link:hover {
    color: inherit !important;
}

.card-header .btn-link i.bi-chevron-down {
    transition: transform 0.2s ease;
}

.card-header .btn-link[aria-expanded="true"] i.bi-chevron-down {
    transform: rotate(180deg);
}

/* Custom footer styling */
.custom-editor-footer {
    display: flex;
    justify-content: flex-end;
}

/* Mobile lesson card styling */
.clickable-card {
    transition: all 0.2s ease-in-out;
    border: 1px solid #dee2e6;
}

    .clickable-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-color: #0066cc;
    }

    .clickable-card:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

.lesson-card {
    border-radius: 8px;
}

    .lesson-card .card-body {
        position: relative;
    }

/* Ensure editor dialog appears above mobile list view */
.e-schedule-dialog {
    z-index: 1049 !important;
}

    .e-schedule-dialog .e-dlg-overlay {
        z-index: 1048 !important;
    }

/* Style the desktop QuickInfo popup edit and delete buttons as outline icon-only buttons */
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit,
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete {
    width: 40px !important;
    min-width: 40px !important;
    height: 40px !important;
    padding: 8px !important;
    border-radius: 4px !important;
    font-size: 16px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.2s !important;
    border: 1px solid !important;
    cursor: pointer !important;
    background: transparent !important;
}


/* Edit button styling */
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit {
    border-color: #0066cc !important;
    color: #0066cc !important;
}

/* Hide button text on desktop - target the actual text content */
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit,
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete {
    font-size: 0 !important;
    text-indent: -9999px !important;
}

/* Ensure icons are still visible by resetting their font-size */
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit::before,
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete::before {
    font-size: 16px !important;
    text-indent: 0 !important;
}

    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit:hover {
        background: #0066cc !important;
        color: white !important;
    }

    /* Add pencil icon to edit button */
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit::before {
        content: "\F4CB" !important; /* Bootstrap pencil icon */
        font-family: "bootstrap-icons" !important;
        font-size: 16px !important;
    }


/* Delete button styling */
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete {
    border-color: #dc3545 !important;
    color: #dc3545 !important;
}

    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete:hover {
        background: #dc3545 !important;
        color: white !important;
    }

    /* Add trash icon to delete button */
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete::before {
        content: "\F5DE" !important; /* Bootstrap trash icon */
        font-family: "bootstrap-icons" !important;
        font-size: 16px !important;
    }

    /* Ensure icons inherit button colors */
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit .e-btn-icon,
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete .e-btn-icon,
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit .e-icons,
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete .e-icons {
        color: inherit !important;
    }

/* Style the button container */
.e-quick-popup-wrapper .e-event-popup .e-popup-footer {
    display: flex !important;
    gap: 8px !important;
    justify-content: flex-end !important;
    padding: 16px !important;
    border-top: 1px solid #eee !important;
}

/* Mobile-specific fixes for Schedule dialog */
@media (max-width: 991.98px) {
    /* Mobile schedule height */
    .schedule-container .e-schedule {
        height: 600px !important;
    }

    /* Fix z-index to appear above sticky header and mobile list view */
    .e-schedule-dialog.e-dialog,
    .e-schedule-dialog {
        z-index: 1050 !important;
    }

        /* Fix dialog positioning and height for mobile */
        .e-schedule-dialog .e-dlg-container {
            max-height: calc(100vh - 80px) !important;
            margin-top: 40px !important;
            margin-bottom: 40px !important;
        }

        /* Make dialog content scrollable */
        .e-schedule-dialog .e-dlg-content {
            max-height: calc(100vh - 160px) !important;
            overflow-y: auto !important;
            padding: 15px !important;
        }

    /* Ensure custom editor is scrollable */
    .custom-event-editor {
        max-height: calc(100vh - 200px) !important;
        overflow-y: auto !important;
        padding: 15px !important;
    }

    /* Force QuickInfo popup to not be full screen on mobile */
    .e-quick-popup-wrapper {
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        width: 90% !important;
        max-width: 400px !important;
        height: auto !important;
        max-height: 80vh !important;
        z-index: 1047 !important;
        bottom: auto !important;
    }

    .e-quick-popup-wrapper .e-event-popup {
        position: relative !important;
        width: 100% !important;
        height: auto !important;
        max-height: none !important;
        transform: none !important;
        top: auto !important;
        left: auto !important;
        right: auto !important;
        bottom: auto !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
    }

    /* Force the popup to fit content exactly */
    .e-quick-popup-wrapper .e-event-popup .e-popup-content {
        height: auto !important;
        min-height: auto !important;
        max-height: none !important;
        overflow: visible !important;
        padding: 16px !important;
    }

    /* Remove any fixed heights that might cause extra space */
    .e-quick-popup-wrapper .e-event-popup .e-popup-content .quick-info {
        height: auto !important;
        min-height: auto !important;
    }

    /* Hide the footer area completely on mobile for all users */
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer {
        display: none !important;
    }

    /* Compact form spacing on mobile */
    .custom-event-editor .row {
        margin-bottom: 0.75rem;
    }

    .custom-event-editor .mb-3 {
        margin-bottom: 0.75rem !important;
    }

    /* Custom footer mobile adjustments */
    .custom-editor-footer {
        padding: 10px 15px;
        gap: 8px;
    }
}

/* Extra small mobile adjustments */
@media (max-width: 575.98px) {
    .e-schedule-dialog .e-dlg-container {
        margin: 10px !important;
        max-height: calc(100vh - 20px) !important;
    }

    .e-schedule-dialog .e-dlg-content {
        max-height: calc(100vh - 100px) !important;
        padding: 10px !important;
    }

    .custom-event-editor {
        max-height: calc(100vh - 120px) !important;
        padding: 10px !important;
    }

        /* Stack form fields vertically on very small screens */
        .custom-event-editor .col-md-6 {
            margin-bottom: 0.5rem;
        }

    /* Custom footer extra small mobile adjustments */
    .custom-editor-footer {
        padding: 8px 10px;
        gap: 6px;
    }

        .custom-editor-footer .btn {
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
        }
}

/* Import/Export button styling */
.btn-group .dropdown-toggle {
    border-top-right-radius: 0.375rem !important;
    border-bottom-right-radius: 0.375rem !important;
}

.dropdown-menu .dropdown-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
}

.dropdown-menu .dropdown-item i {
    width: 16px;
    text-align: center;
}

.dropdown-menu .dropdown-item:hover {
    background-color: #f8f9fa;
}

/* Syncfusion Uploader styling for import button */
.calendar-import {
    display: inline-block;
    height: fit-content;
    align-self: flex-start;
}

/* Hide the entire upload container and show only the button */
.calendar-import .e-upload {
    border: none !important;
    background: none !important;
    width: auto !important;
    min-width: 120px !important;
    height: auto !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Hide all drag and drop related elements */
.calendar-import .e-upload-dnd-wrapper,
.calendar-import .e-upload-dnd,
.calendar-import .e-upload-drag-hover,
.calendar-import .e-upload-files,
.calendar-import .e-upload-initial,
.calendar-import .e-upload-wrap .e-upload-dnd,
.calendar-import .e-upload-wrap .e-upload-dnd-wrapper {
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
    width: 0 !important;
    overflow: hidden !important;
}

/* Style only the file select wrapper */
.calendar-import .e-file-select-wrap {
    display: inline-block !important;
    width: auto !important;
    height: auto !important;
    border: none !important;
    background: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Style the browse button to look like other buttons */
.calendar-import .e-css.e-btn {
    background-color: #198754 !important;
    border-color: #198754 !important;
    color: white !important;
    font-size: 0.875rem !important;
    padding: 0.375rem 0.75rem !important;
    border-radius: 0.375rem !important;
    border: 1px solid #198754 !important;
    min-width: 120px !important;
    height: auto !important;
    line-height: 1.5 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-decoration: none !important;
    vertical-align: middle !important;
    cursor: pointer !important;
    user-select: none !important;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
}

.calendar-import .e-css.e-btn:hover {
    background-color: #157347 !important;
    border-color: #146c43 !important;
    color: white !important;
}

.calendar-import .e-css.e-btn:focus {
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25) !important;
    background-color: #157347 !important;
    border-color: #146c43 !important;
}

.calendar-import .e-css.e-btn:active {
    background-color: #146c43 !important;
    border-color: #13653f !important;
}

/* Add icon to the button */
.calendar-import .e-css.e-btn::before {
    content: "📁 ";
    margin-right: 0.25rem;
}

/* Completely hide the upload wrapper container */
.calendar-import .e-upload-wrap {
    border: none !important;
    background: none !important;
    padding: 0 !important;
    margin: 0 !important;
    box-shadow: none !important;
    min-height: auto !important;
    height: auto !important;
}

/* Hide any text or additional elements */
.calendar-import .e-upload-wrap > *:not(.e-file-select-wrap) {
    display: none !important;
}

/* Additional overrides to ensure clean button appearance */
.calendar-import .e-upload-wrap::after,
.calendar-import .e-upload-wrap::before {
    display: none !important;
}

/* Remove any default Syncfusion spacing */
.calendar-import * {
    box-sizing: border-box;
}

/* Ensure the component takes minimal space */
.calendar-import {
    max-width: fit-content;
    max-height: fit-content;
    overflow: hidden;
}

/* Hide progress bar and status elements */
.calendar-import .e-upload-progress-wrap,
.calendar-import .e-upload-status,
.calendar-import .e-upload-file-status {
    display: none !important;
}

/* Ensure page title is always visible */
.container-fluid h1 {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 1;
    position: relative;
}

/* Hidden uploader styling */
.d-none {
    display: none !important;
}

/* Additional mobile responsive adjustments for import/export */
@media (max-width: 767.98px) {
    /* Stack import/export buttons on mobile */
    .d-flex.gap-2.flex-wrap {
        flex-direction: column;
        align-items: stretch;
    }

    .d-flex.gap-2.flex-wrap > * {
        margin-bottom: 0.5rem;
    }

    .btn-group {
        width: 100%;
    }

    .btn-group .dropdown-toggle {
        width: 100%;
        justify-content: center;
    }

    /* Make import uploader full width on mobile */
    .calendar-import {
        width: 100%;
        flex: 1;
    }

    .calendar-import .e-css.e-btn {
        width: 100% !important;
        justify-content: center !important;
        min-width: 100% !important;
    }
}
