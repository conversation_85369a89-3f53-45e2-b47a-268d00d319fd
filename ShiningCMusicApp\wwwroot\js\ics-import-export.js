// ICS Import/Export JavaScript Functions

/**
 * Triggers the hidden file uploader for ICS import
 */
window.triggerFileUpload = function () {
    try {
        console.log('Attempting to trigger file upload...');

        // Wait a bit for the component to fully render
        setTimeout(() => {
            // Try multiple approaches to find the file input
            let fileInput = null;

            // Approach 1: Find by ID
            const uploaderContainer = document.getElementById('icsUploader');
            console.log('Uploader container found:', uploaderContainer);

            if (uploaderContainer) {
                fileInput = uploaderContainer.querySelector('input[type="file"]');
                console.log('File input found in container:', fileInput);
            }

            // Approach 2: Find by Syncfusion classes
            if (!fileInput) {
                fileInput = document.querySelector('.e-upload input[type="file"]');
                console.log('File input found by Syncfusion class:', fileInput);
            }

            // Approach 3: Find any hidden file input
            if (!fileInput) {
                fileInput = document.querySelector('.d-none input[type="file"]');
                console.log('File input found by d-none class:', fileInput);
            }

            // Approach 4: Find any file input with .ics extension
            if (!fileInput) {
                const allFileInputs = document.querySelectorAll('input[type="file"]');
                console.log('All file inputs found:', allFileInputs.length);
                for (let input of allFileInputs) {
                    if (input.accept && input.accept.includes('.ics')) {
                        fileInput = input;
                        console.log('Found ICS file input:', fileInput);
                        break;
                    }
                }
            }

            if (fileInput) {
                console.log('Triggering click on file input:', fileInput);
                fileInput.click();
            } else {
                console.error('No file input found. Available elements:');
                console.log('- Uploader container:', document.getElementById('icsUploader'));
                console.log('- All file inputs:', document.querySelectorAll('input[type="file"]'));
                console.log('- Syncfusion upload elements:', document.querySelectorAll('.e-upload'));
            }
        }, 100); // Small delay to ensure component is rendered

    } catch (error) {
        console.error('Error triggering file upload:', error);
    }
};

/**
 * Shows a custom notification for successful operations
 * @param {string} message - The message to display
 * @param {string} type - The type of notification (success, error, warning, info)
 */
window.showNotification = function (message, type = 'success') {
    try {
        // Create a simple toast notification
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} position-fixed`;
        toast.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        `;
        toast.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="bi bi-${getIconForType(type)} me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close ms-auto" aria-label="Close"></button>
            </div>
        `;

        document.body.appendChild(toast);

        // Show the toast
        setTimeout(() => {
            toast.style.opacity = '1';
        }, 100);

        // Add close functionality
        const closeBtn = toast.querySelector('.btn-close');
        closeBtn.addEventListener('click', () => {
            hideToast(toast);
        });

        // Auto-hide after 5 seconds
        setTimeout(() => {
            hideToast(toast);
        }, 5000);

    } catch (error) {
        console.error('Error showing notification:', error);
    }
};

/**
 * Gets the appropriate Bootstrap icon for the notification type
 * @param {string} type - The notification type
 * @returns {string} The icon class name
 */
function getIconForType(type) {
    switch (type) {
        case 'success': return 'check-circle-fill';
        case 'error': return 'exclamation-triangle-fill';
        case 'warning': return 'exclamation-triangle-fill';
        case 'info': return 'info-circle-fill';
        default: return 'info-circle-fill';
    }
}

/**
 * Hides a toast notification
 * @param {HTMLElement} toast - The toast element to hide
 */
function hideToast(toast) {
    toast.style.opacity = '0';
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 300);
}

/**
 * Validates if a file is a valid ICS file
 * @param {File} file - The file to validate
 * @returns {boolean} True if valid ICS file
 */
window.validateIcsFile = function (file) {
    try {
        // Check file extension
        if (!file.name.toLowerCase().endsWith('.ics')) {
            return false;
        }

        // Check MIME type (if available)
        if (file.type && file.type !== 'text/calendar') {
            return false;
        }

        return true;
    } catch (error) {
        console.error('Error validating ICS file:', error);
        return false;
    }
};

/**
 * Formats file size for display
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
window.formatFileSize = function (bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

console.log('ICS Import/Export JavaScript functions loaded successfully');
